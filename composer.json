{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.1", "dcat/easy-excel": "^1.1", "dcat/laravel-admin": "2.2.3-beta", "dujiaoka/currency": "*", "dujiaoka/geetest": "*", "laravel/framework": "^10.0", "laravel/tinker": "^2.8", "jenssegers/agent": "^2.6", "mews/captcha": "^3.3", "simplesoftwareio/simple-qrcode": "^4.2", "stripe/stripe-php": "^10.0", "yansongda/pay": "^3.7"}, "require-dev": {"spatie/laravel-ignition": "^2.0", "fakerphp/faker": "^1.21", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^7.0", "phpunit/phpunit": "^10.0"}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "platform-check": false}, "extra": {"laravel": {"dont-discover": []}}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["app/Helpers/functions.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "repositories": [{"type": "path", "url": "./packages/dujiaoka/geetest"}, {"type": "path", "url": "./packages/dujiaoka/currency"}], "minimum-stability": "dev", "prefer-stable": true, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}}