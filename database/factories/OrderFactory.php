<?php

namespace Database\Factories;

use App\Models\Order;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Order>
 */
class OrderFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Order::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'order_sn' => strtoupper(Str::random(12)),
            'goods_id' => rand(1, 3),
            'coupon_id' => rand(1, 3),
            'title' => fake()->words(3, true),
            'type' => rand(1,2),
            'goods_price' => fake()->randomFloat(2, 10, 100),
            'buy_amount' => rand(1, 10),
            'coupon_discount_price' => fake()->randomFloat(2, 0, 100),
            'wholesale_discount_price' => fake()->randomFloat(2, 0, 100),
            'total_price' => fake()->randomFloat(2, 10, 100),
            'actual_price' => fake()->randomFloat(2, 10, 100),
            'search_pwd' => fake()->password(6, 10),
            'email' => fake()->email,
            'info' => fake()->words(3, true),
            'pay_id' => rand(1, 20),
            'buy_ip' => fake()->ipv4,
            'trade_no' => strtoupper(Str::random(12)),
            'status' => rand(1, 5),
            'created_at' => fake()->dateTimeBetween('-7 days', 'now'),
            'updated_at' => fake()->dateTimeBetween('-7 days', 'now'),
        ];
    }
}
