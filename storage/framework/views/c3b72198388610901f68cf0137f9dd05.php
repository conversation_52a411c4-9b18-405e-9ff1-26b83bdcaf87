<div class="<?php echo e($viewClass['form-group'], false); ?>">

    <label class="<?php echo e($viewClass['label'], false); ?> control-label"><?php echo $label; ?></label>

    <div class="<?php echo e($viewClass['field'], false); ?>">

        <?php echo $__env->make('admin::form.error', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

        <input type="text" class="<?php echo e($class, false); ?>" name="<?php echo e($name, false); ?>" data-from="<?php echo e($value, false); ?>" <?php echo $attributes; ?> />

        <?php echo $__env->make('admin::form.help-block', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

    </div>
</div>

<script require="@ionslider" init="<?php echo $selector; ?>">
    setTimeout(function () {
        $this.ionRangeSlider(<?php echo admin_javascript_json($options); ?>)
    }, 400);
</script><?php /**PATH /www/wwwroot/www.bgoom.top/vendor/dcat/laravel-admin/resources/views/form/slider.blade.php ENDPATH**/ ?>