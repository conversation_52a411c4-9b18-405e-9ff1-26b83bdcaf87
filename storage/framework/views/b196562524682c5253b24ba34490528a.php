<div class="input-group input-group-sm">
    <?php if($group): ?>
        <div class="input-group-prepend dropdown">
            <a class="filter-group input-group-text bg-white dropdown-toggle" data-toggle="dropdown">
                <span class="<?php echo e($group_name, false); ?>-label"><?php echo e($default['label'], false); ?>&nbsp; </span>
            </a>
            <input type="hidden" name="<?php echo e($id, false); ?>_group" class="<?php echo e($group_name, false); ?>-operation" value="<?php echo e(request($id.'_group', 0), false); ?>" />
            <ul class="dropdown-menu <?php echo e($group_name, false); ?>">
                <?php $__currentLoopData = $group; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <li class="dropdown-item"><a href="#" data-index="<?php echo e($index, false); ?>"> <?php echo e($item['label'], false); ?> </a></li>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </ul>
        </div>
    <?php endif; ?>

    <div class="input-group-prepend">
        <span class="input-group-text bg-white text-capitalize"><b><?php echo $label; ?></b>&nbsp;<i class="feather icon-calendar"></i></span>
    </div>
    <input class="form-control" id="<?php echo e($id, false); ?>" autocomplete="off" placeholder="<?php echo e($label, false); ?>" name="<?php echo e($name, false); ?>" value="<?php echo e(request($name, $value), false); ?>">
</div>

<script require="@moment,@bootstrap-datetimepicker">
    $('#<?php echo e($id, false); ?>').datetimepicker(<?php echo admin_javascript_json($options); ?>);
</script>
<?php /**PATH /www/wwwroot/www.bgoom.top/vendor/dcat/laravel-admin/resources/views/filter/datetime.blade.php ENDPATH**/ ?>