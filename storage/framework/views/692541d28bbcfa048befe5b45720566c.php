
<?php $__env->startSection('content'); ?>
<div class="row justify-content-center">
    <div class="col-lg-6">
        <div class="page-title-box">
            
            <h4 class="page-title"><?php echo e(__('hyper.qrpay_title'), false); ?></h4>
        </div>
    </div>
</div>
<div class="row justify-content-center">
    <div class="col-lg-6">
        <div class="card border-primary border">
            <div class="card-body">
                <h5 class="card-title text-primary text-center"><?php echo e(__('hyper.qrpay_order_expiration_date'), false); ?> <?php echo e(dujiaoka_config_get('order_expire_time', 5), false); ?> <?php echo e(__('hyper.qrpay_expiration_date'), false); ?></h5>
                <div class="text-center">
                    <img src="data:image/png;base64,<?php echo base64_encode(QrCode::format('png')->size(200)->generate($qr_code)); ?>">
                </div>
                
                <p class="card-text text-center"><?php echo e(__('hyper.qrpay_actual_payment'), false); ?>: <?php echo e($actual_price, false); ?></p>
                <?php if(Agent::isMobile() && isset($jump_payuri)): ?>
                    <p class="errpanl" style="text-align: center"><a href="<?php echo e($jump_payuri, false); ?>" class=""><?php echo e(__('hyper.qrpay_open_app_to_pay'), false); ?></a></p>
                <?php endif; ?>
            </div> <!-- end card-body-->
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('js'); ?>
    <script>
        var getting = {
            url:'<?php echo e(url('check-order-status', ['orderSN' => $orderid]), false); ?>',
            dataType:'json',
            success:function(res) {
                if (res.code == 400001) {
                    window.clearTimeout(timer);
                    $.NotificationApp.send("<?php echo e(__('hyper.qrpay_notice'), false); ?>","<?php echo e(__('hyper.order_pay_timeout'), false); ?>","top-center","rgba(0,0,0,0.2)","warning");
                    setTimeout("window.location.href ='/'",3000);
                }
                if (res.code == 200) {
                    window.clearTimeout(timer);
                    $.NotificationApp.send("<?php echo e(__('hyper.qrpay_notice'), false); ?>","<?php echo e(__('hyper.payment_successful'), false); ?>","top-center","rgba(0,0,0,0.2)","success");
                    setTimeout("window.location.href ='<?php echo e(url('detail-order-sn', ['orderSN' => $orderid]), false); ?>'",3000);
                }
            }
        };
        var timer = window.setInterval(function(){$.ajax(getting)},5000);
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('hyper.layouts.default', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH /www/wwwroot/www.bgoom.top/resources/views/kphyper/static_pages/qrpay.blade.php ENDPATH**/ ?>