<div class="box">
    <?php if(isset($title)): ?>
        <div class="box-header with-border">
            <h3 class="box-title"> <?php echo e($title, false); ?></h3>
        </div>
    <?php endif; ?>

    <div class="box-header with-border">
        <div class="pull-right">
            <?php echo $grid->renderExportButton(); ?>

            <?php echo $grid->renderCreateButton(); ?>

        </div>
        <span>
            <?php echo $grid->renderHeaderTools(); ?>

        </span>
    </div>

    <?php echo $grid->renderFilter(); ?>


    <div class="box-body table-responsive no-padding">
        <ul class="mailbox-attachments clearfix">
            <?php $__currentLoopData = $grid->rows(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <li>
                    <span class="mailbox-attachment-icon has-img">
                        <img src="<?php echo isset($server) ? $server . '/' . $row->column($image_column) : \Illuminate\Support\Facades\Storage::disk(config('admin.upload.disk'))->url($row->column($image_column)); ?>" alt="Attachment">
                    </span>
                    <div class="mailbox-attachment-info">
                        <a href="#" class="mailbox-attachment-name" style="word-break:break-all;">
                            <i class="fa fa-camera"></i>&nbsp;&nbsp;
                            <?php echo isset($text_column) ? $row->column($text_column) : ''; ?>

                        </a>
                        <span class="mailbox-attachment-size">
                          <input type="checkbox" class="grid-item" data-id="<?php echo e($row->id(), false); ?>" />
                            <span class="pull-right">
                                <?php echo $row->column('__actions__'); ?>

                                <a href="<?php echo isset($server) ? $server . '/' . $row->column($image_column) : \Illuminate\Support\Facades\Storage::disk(config('admin.upload.disk'))->url($row->column($image_column)); ?>" target="_blank" download="custom-filename.jpg">
                                    <i class="fa fa-cloud-download"></i>
                                </a>
                            </span>
                        </span>
                    </div>
                </li>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </ul>
    </div>

    <div class="box-footer clearfix">
        <?php echo $grid->paginator(); ?>

    </div>
    <!-- /.box-body -->
</div><?php /**PATH /www/wwwroot/www.bgoom.top/vendor/dcat/laravel-admin/resources/views/grid/image.blade.php ENDPATH**/ ?>