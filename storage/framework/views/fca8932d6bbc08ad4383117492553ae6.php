<div <?php echo $attributes; ?>><textarea style="display:none;"><?php echo $content; ?></textarea></div>

<script first>
    var ele = window.Element;
    Dcat.eMatches = ele.prototype.matches ||
        ele.prototype.msMatchesSelector ||
        ele.prototype.webkitMatchesSelector;
</script>

<script require="@editor-md">
    editormd.markdownToHTML('<?php echo e($id, false); ?>', <?php echo admin_javascript_json($options); ?>);

    Element.prototype.matches = Dcat.eMatches;
</script><?php /**PATH /www/wwwroot/www.bgoom.top/vendor/dcat/laravel-admin/resources/views/widgets/markdown.blade.php ENDPATH**/ ?>