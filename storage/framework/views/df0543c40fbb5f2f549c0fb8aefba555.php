<style>
    .ext-icon {
        color: rgba(0,0,0,0.5);
        margin-left: 10px;
    }
    .installed {
        color: #00a65a;
        margin-right: 15px;
        font-size:20px;
    }
</style>
<ul class="products-list product-list-in-box" id="extension-box" style="margin-top:10px;min-height: 100px">
    <?php $__currentLoopData = $extensions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $extension): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <li class="item hidden">
            <div class="product-img">
                <i class="<?php echo e($extension['icon'], false); ?> fa-2x ext-icon"></i>
            </div>
            <div class="product-info" data-key="<?php echo e($extension['key'], false); ?>">
                <a href="<?php echo e($extension['link'], false); ?>" target="_blank" class="">
                    <?php echo e($extension['name'], false); ?>

                </a>
                <?php if($extension['installed']): ?>
                    <span class="pull-right installed"><i class="ti-check"></i></span>
                <?php endif; ?>
            </div>
        </li>
<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

</ul>

<div class="box-footer text-center">
    <a href="https://github.com/jqhph/dcat-admin#%E6%89%A9%E5%B1%95" target="_blank" class="uppercase">View All Extensions</a>
</div>

<script>Dcat.ready(function () {
    // var $box = $('#extension-box');
    // $box.loading();
    //
    // $.ajax({
    //     url: 'https://jqhph.github.io/dcat-admin/extra/extensions.html',
    //     success: function (response) {
    //         $box.loading(false);
    //
    //         $box.html(response);
    //     },
    //     error: function () {
    //         $box.loading(false);
    //
    //         $box.find('.item').removeClass('hidden');
    //     }
    // });
})</script><?php /**PATH /www/wwwroot/www.bgoom.top/vendor/dcat/laravel-admin/resources/views/dashboard/extensions.blade.php ENDPATH**/ ?>