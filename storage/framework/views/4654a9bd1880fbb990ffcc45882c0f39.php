<div class="grid-selector">
    <?php $__currentLoopData = $self->all(true); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $column => $selector): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <div class="wrap">
            <div class="select-label"><?php echo e($selector['label'], false); ?></div>
            <div class="select-options">
                <ul>
                    <?php $__currentLoopData = $selector['options']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $value => $option): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <?php
                            $active = in_array((string) $value, \Illuminate\Support\Arr::get($selected, $column, []), true);
                        ?>
                        <li>
                            <a href="<?php echo e($self->url($column, $value, true), false); ?>"
                               class="<?php echo e($active ? 'active' : '', false); ?>"><?php echo e($option, false); ?></a>
                            <?php if(!$active && $selector['type'] == 'many'): ?>
                                &nbsp;
                                <a href="<?php echo e($self->url($column, $value), false); ?>" class="add"><i class="feather icon-plus-square"></i></a>
                            <?php else: ?>
                                <a style="visibility: hidden;"><i class="feather icon-plus-square"></i></a>
                            <?php endif; ?>
                        </li>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    <li>
                        <a href="<?php echo e($self->url($column), false); ?>" class="clear"><i class="feather icon-trash-2"></i></a>
                    </li>
                </ul>
            </div>
        </div>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
</div>
<?php /**PATH /www/wwwroot/www.bgoom.top/vendor/dcat/laravel-admin/resources/views/grid/selector.blade.php ENDPATH**/ ?>