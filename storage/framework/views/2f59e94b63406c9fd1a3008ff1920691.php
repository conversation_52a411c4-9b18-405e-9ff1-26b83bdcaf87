<div class="<?php echo e($viewClass['form-group'], false); ?>">
    <label class="<?php echo e($viewClass['label'], false); ?> control-label"><?php echo $label; ?></label>
    <div class="<?php echo e($viewClass['field'], false); ?> select-resource">
        <?php echo $__env->make('admin::form.error', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

        <div class="input-group">
            <div <?php echo $attributes; ?>>
                <span class="default-text" style="opacity:0.75"><?php echo e($placeholder, false); ?></span>
                <span class="option d-none"></span>

                <?php if(! $disabled): ?>
                    <input name="<?php echo e($name, false); ?>" type="hidden" value="<?php echo e(implode(',', Dcat\Admin\Support\Helper::array($value)), false); ?>" />
                <?php endif; ?>
            </div>

            <div class="input-group-append">
                <?php echo $dialog; ?>

            </div>
        </div>

        <?php echo $__env->make('admin::form.help-block', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

    </div>
</div>

<script require="@select-table" init="<?php echo $selector; ?>">
    var dialogId = $this.parent().find('<?php echo $dialogSelector; ?>').attr('id');
    var $input = $(this).find('input');

    Dcat.grid.SelectTable({
        dialog: '[data-id="' + dialogId + '"]',
        container: $this,
        input: $input,
        <?php if(isset($max)): ?>
        multiple: true,
        max: <?php echo e($max, false); ?>,
        <?php endif; ?>
        values: <?php echo json_encode($options); ?>,
    });

    <?php if(! empty($loads)): ?>
    var fields = '<?php echo $loads['fields']; ?>'.split('^');
    var urls = '<?php echo $loads['urls']; ?>'.split('^');

    $input.on('change', function () {
        var values = this.value;

        Dcat.helpers.loadFields(this, {
            group: '.fields-group',
            urls: urls,
            fields: fields,
            textField: "<?php echo e($loads['textField'], false); ?>",
            idField: "<?php echo e($loads['idField'], false); ?>",
            values: values,
        });
    }).trigger('change');
    <?php endif; ?>
</script><?php /**PATH /www/wwwroot/www.bgoom.top/vendor/dcat/laravel-admin/resources/views/form/selecttable.blade.php ENDPATH**/ ?>