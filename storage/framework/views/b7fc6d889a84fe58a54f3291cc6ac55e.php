<style>
    .editormd-fullscreen {z-index: 99999999;}
</style>

<div class="<?php echo e($viewClass['form-group'], false); ?>">

    <label class="<?php echo e($viewClass['label'], false); ?> control-label"><?php echo $label; ?></label>

    <div class="<?php echo e($viewClass['field'], false); ?>">

        <?php echo $__env->make('admin::form.error', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

        <div class="<?php echo e($class, false); ?>" <?php echo $attributes; ?>>
            <textarea class="d-none" name="<?php echo e($name, false); ?>" placeholder="<?php echo e($placeholder, false); ?>"><?php echo $value; ?></textarea>
        </div>

        <?php echo $__env->make('admin::form.help-block', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

    </div>
</div>

<script first>
    var ele = window.Element;
    Dcat.eMatches = ele.prototype.matches ||
        ele.prototype.msMatchesSelector ||
        ele.prototype.webkitMatchesSelector;
</script>

<script require="@editor-md-form" init="<?php echo $selector; ?>">
    editormd(id, <?php echo $options; ?>);

    Element.prototype.matches = Dcat.eMatches;
</script>
<?php /**PATH /www/wwwroot/www.bgoom.top/vendor/dcat/laravel-admin/resources/views/form/markdown.blade.php ENDPATH**/ ?>