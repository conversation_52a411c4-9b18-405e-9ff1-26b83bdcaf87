<style>
    .popover{z-index:29891015}
</style>

<div class="<?php echo e($viewClass['form-group'], false); ?>">
    <div  class="<?php echo e($viewClass['label'], false); ?> control-label">
        <span><?php echo $label; ?></span>
    </div>

    <div class="<?php echo e($viewClass['field'], false); ?>">
        <?php echo $__env->make('admin::form.error', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

        <div class="input-group">

            <span class="input-group-prepend"><span class="input-group-text bg-white" style="padding: 4px"><i style="width: 24px;height: 100%;background: <?php echo $value; ?>"></i></span></span>

            <input <?php echo $attributes; ?> />

            <?php if($append): ?>
                <span class="input-group-append"><?php echo $append; ?></span>
            <?php endif; ?>
        </div>

        <?php echo $__env->make('admin::form.help-block', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    </div>
</div>

<script require="@color" init="<?php echo $selector; ?>">
    $this.colorpicker(<?php echo admin_javascript_json($options); ?>).on('colorpickerChange', function(event) {
        $(this).parents('.input-group').find('.input-group-prepend i').css('background-color', event.color.toString());
    });
</script><?php /**PATH /www/wwwroot/www.bgoom.top/vendor/dcat/laravel-admin/resources/views/form/color.blade.php ENDPATH**/ ?>