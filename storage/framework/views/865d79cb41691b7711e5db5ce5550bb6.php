<style>
    .nav-tabs > li:hover > i{
        display: inline;
    }
    .close-tab {
        position: absolute;
        font-size: 10px;
        top: 20px;
        right: 0;
        cursor: pointer;
        display: none;
    }
</style>
<div class="nav-tabs-custom has-many-<?php echo e($columnClass, false); ?>">
    <div class="row header">
        <div class="<?php echo e($viewClass['label'], false); ?>"><h4 class="pull-right"><?php echo $label; ?></h4></div>
        <div class="<?php echo e($viewClass['field'], false); ?>" style="margin-bottom: 5px">
            <div class="add btn btn-outline-primary btn-sm"><i class="feather icon-plus"></i>&nbsp;<?php echo e(trans('admin.new'), false); ?></div>
        </div>
    </div>

    <hr class="mb-0 mt-0">

    <ul class="nav nav-tabs">
        <?php $__currentLoopData = $forms; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $pk => $form): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <li class="nav-item ">
                <a href="#<?php echo e($relationName . '_' . $pk, false); ?>" class="nav-link <?php if($form == reset($forms)): ?> active <?php endif; ?> " data-toggle="tab">
                    <?php echo e($pk, false); ?> <i class="feather icon-alert-circle text-red d-none"></i>
                </a>
                <i class="close-tab feather icon-trash text-red"></i>
            </li>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

    </ul>
    
    <div class="tab-content has-many-<?php echo e($columnClass, false); ?>-forms">

        <?php $__currentLoopData = $forms; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $pk => $form): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="tab-pane fields-group has-many-<?php echo e($columnClass, false); ?>-form <?php if($form == reset($forms)): ?> active <?php endif; ?>" id="<?php echo e($relationName . '_' . $pk, false); ?>">
                <?php echo $form->render(); ?>

            </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </div>

    <template class="nav-tab-tpl">
        <li class="new nav-item">
            <a href="#<?php echo e($relationName . '_new_' . \Dcat\Admin\Form\NestedForm::DEFAULT_KEY_NAME, false); ?>" class="nav-link" data-toggle="tab">
                &nbsp;New <?php echo e(\Dcat\Admin\Form\NestedForm::DEFAULT_KEY_NAME, false); ?> <i class="feather icon-alert-circle text-red d-none"></i>
            </a>
            <i class="close-tab feather icon-trash text-red" ></i>
        </li>
    </template>
    <template class="pane-tpl">
        <div class="tab-pane fields-group new" id="<?php echo e($relationName . '_new_' . Dcat\Admin\Form\NestedForm::DEFAULT_KEY_NAME, false); ?>">
            <?php echo $template; ?>

        </div>
    </template>

</div>

<script>
    var container = '.has-many-<?php echo e($columnClass, false); ?>';
    
    $(container+' > .nav').off('click', 'i.close-tab').on('click', 'i.close-tab', function(){
        var $navTab = $(this).siblings('a');
        var $pane = $($navTab.attr('href'));
        if( $pane.hasClass('new') ){
            $pane.remove();
        }else{
            $pane.removeClass('active').find('.<?php echo e(Dcat\Admin\Form\NestedForm::REMOVE_FLAG_CLASS, false); ?>').val(1);
        }
        if($navTab.closest('li').hasClass('active')){
            $navTab.closest('li').remove();
            $(container+' > .nav > li:nth-child(1) > a').click();
        }else{
            $navTab.closest('li').remove();
        }
    });

    var nestedIndex = <?php echo $count; ?>;

    function replaceNestedFormIndex(value) {
        return String(value).replace(/<?php echo e(Dcat\Admin\Form\NestedForm::DEFAULT_KEY_NAME, false); ?>/g, nestedIndex);
    }

    $(container+' > .header').off('click', '.add').on('click', '.add', function(){
        nestedIndex++;
        var navTabHtml = replaceNestedFormIndex($(container+' > template.nav-tab-tpl').html());
        var paneHtml = replaceNestedFormIndex($(container+' > template.pane-tpl').html());
        $(container+' > .nav').append(navTabHtml);
        $(container+' > .tab-content').append(paneHtml);
        $(container+' > .nav > li:last-child a').click();
    });

    if ($('.has-error').length) {
        $('.has-error').parent('.tab-pane').each(function () {
            var tabId = '#'+$(this).attr('id');
            $('li a[href="'+tabId+'"] i').removeClass('d-none');
        });

        var first = $('.has-error:first').parent().attr('id');
        $('li a[href="#'+first+'"]').tab('show');
    }
</script><?php /**PATH /www/wwwroot/www.bgoom.top/vendor/dcat/laravel-admin/resources/views/form/hasmanytab.blade.php ENDPATH**/ ?>