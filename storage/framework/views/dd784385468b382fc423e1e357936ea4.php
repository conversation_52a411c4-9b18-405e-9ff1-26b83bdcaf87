<div <?php echo $attributes; ?>>
    <div class="dropdown btn-group <?php echo $options['show_tool_shadow'] ? '' : 'no-shadow'; ?> pull-right">
        <?php $__currentLoopData = $options['tools']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tool): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <?php echo $tool; ?>

        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </div>

    <h4 class="header-title m-t-0 m-b-25"><?php echo $options['title']; ?></h4>

    <div>
        <div>
            <div class="right-content pull-right"><?php echo $options['content']['right']; ?></div>

            <h2 class="main-content m-b-10" >
                <?php echo $options['content']['left']; ?>&nbsp;
            </h2>
            <p class="text-muted">
                <?php echo $options['description']; ?>&nbsp;
            </p>
        </div>

        <?php if($options['progress']): ?>
        <div class="progress progress-sm m-b-0">
            <div data-width="<?php echo $options['progress']['percent']; ?>%" class="progress-bar progress-bar-<?php echo $options['progress']['style']; ?>" >
                <span class="sr-only"></span>
            </div>
        </div>
        <?php endif; ?>

    </div>
</div><?php /**PATH /www/wwwroot/www.bgoom.top/vendor/dcat/laravel-admin/resources/views/widgets/data-card.blade.php ENDPATH**/ ?>