<div class="<?php echo e($viewClass['form-group'], false); ?>">

    <label class="<?php echo e($viewClass['label'], false); ?> control-label"><?php echo $label; ?></label>

    <div class="<?php echo e($viewClass['field'], false); ?>">

        <?php echo $__env->make('admin::form.error', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

        <div class="input-group" style="width: 250px;">

            <input <?php echo $attributes; ?> />

            <span class="input-group-addon clearfix" style="padding: 1px;">
                <img class="field-refresh-captcha" data-url="<?php echo e($captchaSrc, false); ?>" src="<?php echo e($captchaSrc, false); ?>" style="height:30px;cursor: pointer;"  title="Click to refresh"/>
            </span>
        </div>

        <?php echo $__env->make('admin::form.help-block', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

    </div>
</div>

<script init=".field-refresh-captcha" once>
    $this.off('click').on('click', function () {
        $(this).attr('src', $(this).attr('data-url')+'?'+Math.random());
    });
</script><?php /**PATH /www/wwwroot/www.bgoom.top/vendor/dcat/laravel-admin/resources/views/form/captcha.blade.php ENDPATH**/ ?>