
<style>
    table.grid-switch-group tr td {
        padding: 3px 0!important;
        height:25px!important;
        border: 0!important;
    }
</style>

<table class="grid-switch-group">
    <?php $__currentLoopData = $columns; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $column => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <?php ($checked = Illuminate\Support\Arr::get($row, $column) ? 'checked' : ''); ?>

        <tr style="box-shadow: none;background: transparent">
            <td><?php echo e($label, false); ?>:&nbsp;&nbsp;&nbsp;</td>
            <td><input name="<?php echo e($column, false); ?>" data-path="<?php echo e($resource, false); ?>" data-key="<?php echo e($key, false); ?>" <?php echo e($checked, false); ?>

                                                           type="checkbox" class="grid-column-switch-group" data-size="small" data-color="<?php echo e($color, false); ?>"/></td>
        </tr>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
</table>

<script require="@switchery">
    var swt = $('.grid-column-switch-group'),
        reload = '<?php echo e($refresh, false); ?>',
        that;
    function initSwitchery() {
        swt.each(function() {
            that = $(this);
            that.parent().find('.switchery').remove();

            new Switchery(that[0], that.data())
        })
    }
    initSwitchery();
    swt.off('change').change(function(e) {
        var that = $(this),
            id = that.data('key'),
            url = that.data('path') + '/' + id,
            checked = that.is(':checked'),
            name = that.attr('name'),
            data = {},
            value = checked ? 1 : 0;

        if (name.indexOf('.') === -1) {
            data[name] = value;
        } else {
            name = name.split('.');

            data[name[0]] = {};
            data[name[0]][name[1]] = value;
        }
        Dcat.NP.start();

        $.put({
            url: url,
            data: data,
            success: function (d) {
                Dcat.NP.done();
                var msg = d.data.message || d.message;
                if (d.status) {
                    Dcat.success(msg);
                    reload && Dcat.reload()
                } else {
                    Dcat.error(msg);
                }
            }
        });
    });
</script>
<?php /**PATH /www/wwwroot/www.bgoom.top/vendor/dcat/laravel-admin/resources/views/grid/displayer/switchgroup.blade.php ENDPATH**/ ?>