
<div class="row" style="margin-top: 10px;">
    <div class="<?php echo e($viewClass['label'], false); ?>"><h4 class="pull-right"><?php echo $label; ?></h4></div>
    <div class="<?php echo e($viewClass['field'], false); ?>"></div>
</div>

<hr class="mt-0">

<div class="has-many-<?php echo e($columnClass, false); ?>">

    <div class="has-many-<?php echo e($columnClass, false); ?>-forms">

        <?php $__currentLoopData = $forms; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $pk => $form): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>

            <div class="has-many-<?php echo e($columnClass, false); ?>-form fields-group">

                <?php echo $form->render(); ?>


                <?php if($options['allowDelete']): ?>
                    <div class="form-group row">
                        <label class="<?php echo e($viewClass['label'], false); ?> control-label"></label>
                        <div class="<?php echo e($viewClass['field'], false); ?>">
                            <div class="<?php echo e($columnClass, false); ?>-remove btn btn-white btn-sm pull-right"><i class="feather icon-trash">&nbsp;</i><?php echo e(trans('admin.remove'), false); ?></div>
                        </div>
                    </div>
                <?php endif; ?>
                <hr>
            </div>

        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </div>


    <template class="<?php echo e($columnClass, false); ?>-tpl">
        <div class="has-many-<?php echo e($columnClass, false); ?>-form fields-group">

            <?php echo $template; ?>


            <div class="form-group row">
                <label class="<?php echo e($viewClass['label'], false); ?> control-label"></label>
                <div class="<?php echo e($viewClass['field'], false); ?>">
                    <div class="<?php echo e($columnClass, false); ?>-remove btn btn-white btn-sm pull-right"><i class="feather icon-trash"></i>&nbsp;<?php echo e(trans('admin.remove'), false); ?></div>
                </div>
            </div>
            <hr>
        </div>
    </template>

    <?php if($options['allowCreate']): ?>
        <div class="form-group row">
            <label class="<?php echo e($viewClass['label'], false); ?> control-label"></label>
            <div class="<?php echo e($viewClass['field'], false); ?>">
                <div class="<?php echo e($columnClass, false); ?>-add btn btn-primary btn-outline btn-sm"><i class="feather icon-plus"></i>&nbsp;<?php echo e(trans('admin.new'), false); ?></div>
            </div>
        </div>
    <?php endif; ?>

</div>

<script>
    var nestedIndex = <?php echo $count; ?>,
        container = '.has-many-<?php echo e($columnClass, false); ?>',
        forms = '.has-many-<?php echo e($columnClass, false); ?>-forms';

    function replaceNestedFormIndex(value) {
        return String(value)
            .replace(/<?php echo e(Dcat\Admin\Form\NestedForm::DEFAULT_KEY_NAME, false); ?>/g, nestedIndex)
            .replace(/<?php echo e(Dcat\Admin\Form\NestedForm::DEFAULT_PARENT_KEY_NAME, false); ?>/g, nestedIndex);
    }

    $(container).on('click', '.<?php echo e($columnClass, false); ?>-add', function () {
        var tpl = $('template.<?php echo e($columnClass, false); ?>-tpl');

        nestedIndex++;

        $(forms).append(replaceNestedFormIndex(tpl.html()));
    });

    $(container).on('click', '.<?php echo e($columnClass, false); ?>-remove', function () {
        var $form = $(this).closest('.has-many-<?php echo e($columnClass, false); ?>-form');

        $form.hide();
        $form.find('.<?php echo e(Dcat\Admin\Form\NestedForm::REMOVE_FLAG_CLASS, false); ?>').val(1);
        $form.find('[required]').prop('required', false);
    });
</script>
<?php /**PATH /www/wwwroot/www.bgoom.top/vendor/dcat/laravel-admin/resources/views/form/hasmany.blade.php ENDPATH**/ ?>