<?php $__env->startSection('content'); ?>
<div class="row invite-card mt-3">
    <div class="col col-xs-12 col-sm-12 col-md-12 col-lg-12">
        <div class="card">
            <div class="card">
                <div class="card-body">
                    <div class="table-responsive invite-table">
                        <table class="table table-hover">
                            <thead>
                            <tr>
                                <th scope="col">ID</th>
                                <th scope="col">图片</th>
                                <th scope="col">商品名称</th>
                                <th scope="col">原价</th>
                                <th scope="col"><?php echo e(Auth::user()->grade, false); ?>级代理价</th>
                                <th scope="col">库存</th>
                                <th scope="col">操作</th>
                            </tr>
                            </thead>
                            <tbody>
                            <?php $__currentLoopData = $goods; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr>
                                <td scope="row" style="vertical-align: middle;"><?php echo e($item->id, false); ?></td>
                                <td style="vertical-align: middle;">
                                    <img width="30" class="home-img" src="/assets/hyper/images/loading.gif" data-src="<?php echo e(picture_ulr($goods['picture']), false); ?>">
                                </td>
                                <td class="text-primary" style="vertical-align: middle;"><?php echo e($item->gd_name, false); ?></td>
                                <td style="vertical-align: middle;">￥<?php echo e($item->actual_price, false); ?><?php echo e((dujiaoka_config_get('global_currency')), false); ?></td>
                                <td style="vertical-align: middle;">￥<?php echo e(\App\Service\Util::getGradePrice($item), false); ?><?php echo e((dujiaoka_config_get('global_currency')), false); ?></td>
                                <td style="vertical-align: middle;"><?php echo e(\App\Service\Util::getStock($item), false); ?></td>
                                <td style="vertical-align: middle;">
                                    <button class="btn btn-primary" onclick="onWholesale(<?php echo e($item, false); ?>)">批发</button>
                                </td>
                            </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                        <?php echo e($goods->links(), false); ?>

                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="modal fade" id="wholesaleModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-sm modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel">商品批发</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="buy-form" action="<?php echo e(url('/user/wholesale'), false); ?>" method="post">
                    <?php echo csrf_field(); ?>
                    <input type="hidden" name="goods_id" value="">
                    <div class="form-group buy-group">
                        <div class="buy-title">最小批发数：</div>
                        <span id="min_count"></span>
                    </div>
                    <div class="form-group buy-group">
                        <div class="buy-title">最大批发数：</div>
                        <span id="max_count"></span>
                    </div>
                    <div class="form-group buy-group">
                        <div class="buy-title">批发数：</div>
                        <input type="number" name="count" min="1" class="form-control" placeholder="请输入需要批发的数量">
                    </div>
                    <div class="mt-4 text-center">
                        <button type="submit" class="btn btn-danger" id="onSubmit">
                            <i class="mdi mdi-truck-fast mr-1"></i>
                            点击批发
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('js'); ?>
<script>
    var goods = {};
   function onWholesale(goods) {
       goods = goods;
       $("#max_count").html(goods.max_buy_count);
       $("#min_count").html(goods.min_buy_count);
       $("input[name='goods_id']").val(goods.id);
       $("#wholesaleModal").modal();
   }
    $('#onSubmit').click(function () {
        var count = $("input[name='count']").val();
        var max_buy_count = $("#max_count").html();
        var min_buy_count = $("#min_count").html();
        if (count > parseInt(max_buy_count)){
            $.NotificationApp.send("<?php echo e(__('hyper.buy_warning'), false); ?>", "请不要大于最大批发数", "top-center", "rgba(0,0,0,0.2)", "info");
            return false;
        }
        if (count < parseInt(min_buy_count)){
            $.NotificationApp.send("<?php echo e(__('hyper.buy_warning'), false); ?>", "请不要小于最小批发数", "top-center", "rgba(0,0,0,0.2)", "info");
            return false;
        }
    })
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('hyper.layouts.default', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH /www/wwwroot/www.bgoom.top/resources/views/kphyper/static_pages/wholesale.blade.php ENDPATH**/ ?>