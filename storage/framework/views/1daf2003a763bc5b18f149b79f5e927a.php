<div class="input-group input-group-sm">
    <?php
        $checkbox = new \Dcat\Admin\Widgets\Checkbox($name.'[]', $options);
        if ($inline) $checkbox->inline();

        $checkbox->check(request($name, is_null($value) ? [] : $value))->circle(false);

    ?>
    <?php if($showLabel): ?>
        <div class="pull-left text-capitalize" style="margin-top: 6px;margin-right: 15px;">
            <b><?php echo e($label, false); ?></b>
        </div>
        <div class="pull-left">
            <?php echo $checkbox; ?>

        </div>
    <?php else: ?>
        <?php echo $checkbox; ?>

    <?php endif; ?>
</div>
<?php /**PATH /www/wwwroot/www.bgoom.top/vendor/dcat/laravel-admin/resources/views/filter/checkbox.blade.php ENDPATH**/ ?>