<div class="input-group input-group-sm quick-form-field">
    <select class="form-control <?php echo e($class, false); ?>" style="width: 100%;" name="<?php echo e($name, false); ?>[]" multiple="multiple" data-placeholder="<?php echo e($label, false); ?>" <?php echo $attributes; ?> >
        <?php $__currentLoopData = $options; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $option): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <option value="<?php echo e($keyAsValue ? $key : $option, false); ?>" <?php echo e(in_array($option, $value) ? 'selected' : '', false); ?>><?php echo e($option, false); ?></option>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </select>
    <input type="hidden" name="<?php echo e($name, false); ?>[]" />
</div><?php /**PATH /www/wwwroot/www.bgoom.top/vendor/dcat/laravel-admin/resources/views/grid/quick-create/tags.blade.php ENDPATH**/ ?>