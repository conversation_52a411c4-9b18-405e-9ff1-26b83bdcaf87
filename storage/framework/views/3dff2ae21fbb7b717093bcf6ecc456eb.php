<style>
    td .form-group {margin-bottom: 0 !important;}
</style>

<div class="<?php echo e($viewClass['form-group'], false); ?> <?php echo e($class, false); ?>">

    <label class="<?php echo e($viewClass['label'], false); ?> control-label"><?php echo e($label, false); ?></label>

    <div class="<?php echo e($viewClass['field'], false); ?>">

        <div class="help-block with-errors"></div>

        <span name="<?php echo e($name, false); ?>"></span>
        <input name="<?php echo e($name, false); ?>[values][<?php echo e(Dcat\Admin\Form\Field\ListField::DEFAULT_FLAG_NAME, false); ?>]" type="hidden" />

        <table class="table table-hover">

            <tbody class="list-table">

            <?php $__currentLoopData = ($value ?: []); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $k => $v): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <tr>
                    <td>
                        <div class="form-group">
                            <div class="col-sm-12">
                                <input name="<?php echo e($name, false); ?>[values][<?php echo e((int) $k, false); ?>]" value="<?php echo e($v, false); ?>" class="form-control" />
                                <div class="help-block with-errors"></div>
                            </div>
                        </div>
                    </td>

                    <td style="width: 85px;">
                        <div class="<?php echo e($class, false); ?>-remove list-remove btn btn-white btn-sm pull-right">
                            <i class="feather icon-trash">&nbsp;</i>
                        </div>
                    </td>
                </tr>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </tbody>
            <tfoot>
            <tr>
                <td colspan="2">
                    <div class="list-add btn btn-primary btn-outline btn-sm pull-left">
                        <i class="feather icon-save"></i>&nbsp;<?php echo e(__('admin.new'), false); ?>

                    </div>
                    <div class="text-center">
                        <?php echo $__env->make('admin::form.help-block', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    </div>
                </td>
            </tr>
            </tfoot>
        </table>
    </div>

    <template>
        <tr>
            <td>
                <div class="form-group">
                    <div class="col-sm-12">
                        <input name="<?php echo e($name, false); ?>[values][{key}]" class="form-control" />
                        <div class="help-block with-errors"></div>
                    </div>
                </div>
            </td>

            <td style="width: 85px;">
                <div class="list-remove btn btn-white btn-sm pull-right">
                    <i class="feather icon-trash">&nbsp;</i>
                </div>
            </td>
        </tr>
    </template>
</div>

<script init="<?php echo $selector; ?>">
    var index = <?php echo e($count, false); ?>;
    $this.find('.list-add').on('click', function () {
        var tpl = $this.find('template').html().replace('{key}', index);
        $this.find('tbody.list-table').append(tpl);

        index++;
    });
    $this.find('tbody.list-table').on('click', '.list-remove', function () {
        $(this).closest('tr').remove();
    });
</script>
<?php /**PATH /www/wwwroot/www.bgoom.top/vendor/dcat/laravel-admin/resources/views/form/listfield.blade.php ENDPATH**/ ?>