<style>
    td .form-group {margin-bottom: 0 !important;}
</style>

<div class="<?php echo e($viewClass['form-group'], false); ?> <?php echo e($class, false); ?>">

    <label class="<?php echo e($viewClass['label'], false); ?> control-label"><?php echo e($label, false); ?></label>

    <div class="<?php echo e($viewClass['field'], false); ?>">
        <span name="<?php echo e($name, false); ?>"></span>
        <input name="<?php echo e($name, false); ?>[<?php echo e(\Dcat\Admin\Form\Field\KeyValue::DEFAULT_FLAG_NAME, false); ?>]" type="hidden" />

        <div class="help-block with-errors"></div>

        <table class="table table-hover">
            <thead>
            <tr>
                <th><?php echo $keyLabel; ?></th>
                <th><?php echo $valueLabel; ?></th>
                <th style="width: 85px;"></th>
            </tr>
            </thead>
            <tbody class="kv-table">

            <?php $__currentLoopData = ($value ?: []); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $k => $v): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <tr>
                    <td>
                        <div class="form-group">
                            <div class="col-sm-12">
                                <div class="help-block with-errors"></div>

                                <input name="<?php echo e($name, false); ?>[keys][<?php echo e($loop->index, false); ?>]" value="<?php echo e($k, false); ?>" class="form-control" required/>

                            </div>
                        </div>
                    </td>
                    <td>
                        <div class="form-group">
                            <div class="col-sm-12">
                                <div class="help-block with-errors"></div>
                                <input name="<?php echo e($name, false); ?>[values][<?php echo e($loop->index, false); ?>]" value="<?php echo e($v, false); ?>" class="form-control" />
                            </div>
                        </div>
                    </td>

                    <td class="form-group">
                        <div>
                            <div class="kv-remove btn btn-white btn-sm pull-right">
                                <i class="feather icon-trash">&nbsp;</i>
                            </div>
                        </div>
                    </td>
                </tr>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </tbody>
            <tfoot>
            <tr>
                <td></td>
                <td></td>
                <td>
                    <div class="kv-add btn btn-primary btn-outline btn-sm pull-right">
                        <i class="feather icon-save"></i>&nbsp;<?php echo e(__('admin.new'), false); ?>

                    </div>
                </td>
            </tr>
            </tfoot>
        </table>
    </div>

    <template>
        <tr>
            <td>
                <div class="form-group  ">
                    <div class="col-sm-12">
                        <div class="help-block with-errors"></div>
                        <input name="<?php echo e($name, false); ?>[keys][{key}]" class="form-control" required/>
                    </div>
                </div>
            </td>
            <td>
                <div class="form-group  ">
                    <div class="col-sm-12">
                        <div class="help-block with-errors"></div>
                        <input name="<?php echo e($name, false); ?>[values][{key}]" class="form-control" />
                    </div>
                </div>
            </td>

            <td class="form-group">
                <div>
                    <div class="kv-remove btn btn-white btn-sm pull-right">
                        <i class="feather icon-trash">&nbsp;</i>
                    </div>
                </div>
            </td>
        </tr>
    </template>
</div>

<script init="<?php echo $selector; ?>">
    var index = <?php echo e($count, false); ?>;
    $this.find('.kv-add').on('click', function () {
        var tpl = $this.find('template').html().replace('{key}', index).replace('{key}', index);
        $this.find('tbody.kv-table').append(tpl);

        index++;
    });

    $this.find('tbody.kv-table').on('click', '.kv-remove', function () {
        $(this).closest('tr').remove();
    });
</script><?php /**PATH /www/wwwroot/www.bgoom.top/vendor/dcat/laravel-admin/resources/views/form/keyvalue.blade.php ENDPATH**/ ?>