<?php $__env->startSection('field'); ?>
    <?php echo $radio; ?>

<?php $__env->stopSection(); ?>

<script>
<?php $__env->startSection('popover-content'); ?>
    $template.find('input[type=radio]').each(function (index, checkbox) {
        if(String($(checkbox).attr('value')) === String($trigger.data('value'))) {
            $(checkbox).attr('checked', true);
        }
    });
<?php $__env->stopSection(); ?>
</script>

<?php echo $__env->make('admin::grid.displayer.editinline.template', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH /www/wwwroot/www.bgoom.top/vendor/dcat/laravel-admin/resources/views/grid/displayer/editinline/radio.blade.php ENDPATH**/ ?>