[2025-07-29 07:53:10] local.ERROR: Class "UserController" does not exist {"exception":"[object] (ReflectionException(code: -1): Class \"UserController\" does not exist at /www/wwwroot/www.bgoom.top/vendor/laravel/framework/src/Illuminate/Foundation/Console/RouteListCommand.php:225)
[stacktrace]
#0 /www/wwwroot/www.bgoom.top/vendor/laravel/framework/src/Illuminate/Foundation/Console/RouteListCommand.php(225): ReflectionClass->__construct()
#1 /www/wwwroot/www.bgoom.top/vendor/laravel/framework/src/Illuminate/Foundation/Console/RouteListCommand.php(147): Illuminate\\Foundation\\Console\\RouteListCommand->isVendorRoute()
#2 /www/wwwroot/www.bgoom.top/vendor/laravel/framework/src/Illuminate/Foundation/Console/RouteListCommand.php(116): Illuminate\\Foundation\\Console\\RouteListCommand->getRouteInformation()
#3 [internal function]: Illuminate\\Foundation\\Console\\RouteListCommand->Illuminate\\Foundation\\Console\\{closure}()
#4 /www/wwwroot/www.bgoom.top/vendor/laravel/framework/src/Illuminate/Collections/Arr.php(600): array_map()
#5 /www/wwwroot/www.bgoom.top/vendor/laravel/framework/src/Illuminate/Collections/Collection.php(778): Illuminate\\Support\\Arr::map()
#6 /www/wwwroot/www.bgoom.top/vendor/laravel/framework/src/Illuminate/Foundation/Console/RouteListCommand.php(115): Illuminate\\Support\\Collection->map()
#7 /www/wwwroot/www.bgoom.top/vendor/laravel/framework/src/Illuminate/Foundation/Console/RouteListCommand.php(101): Illuminate\\Foundation\\Console\\RouteListCommand->getRoutes()
#8 /www/wwwroot/www.bgoom.top/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Foundation\\Console\\RouteListCommand->handle()
#9 /www/wwwroot/www.bgoom.top/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#10 /www/wwwroot/www.bgoom.top/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#11 /www/wwwroot/www.bgoom.top/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#12 /www/wwwroot/www.bgoom.top/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()
#13 /www/wwwroot/www.bgoom.top/vendor/laravel/framework/src/Illuminate/Console/Command.php(211): Illuminate\\Container\\Container->call()
#14 /www/wwwroot/www.bgoom.top/vendor/symfony/console/Command/Command.php(326): Illuminate\\Console\\Command->execute()
#15 /www/wwwroot/www.bgoom.top/vendor/laravel/framework/src/Illuminate/Console/Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#16 /www/wwwroot/www.bgoom.top/vendor/symfony/console/Application.php(1096): Illuminate\\Console\\Command->run()
#17 /www/wwwroot/www.bgoom.top/vendor/symfony/console/Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand()
#18 /www/wwwroot/www.bgoom.top/vendor/symfony/console/Application.php(175): Symfony\\Component\\Console\\Application->doRun()
#19 /www/wwwroot/www.bgoom.top/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(201): Symfony\\Component\\Console\\Application->run()
#20 /www/wwwroot/www.bgoom.top/artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#21 {main}
"} 
[2025-07-29 07:55:40] local.ERROR: PHP Parse error: Syntax error, unexpected '>' on line 1 {"exception":"[object] (Psy\\Exception\\ParseErrorException(code: 0): PHP Parse error: Syntax error, unexpected '>' on line 1 at /www/wwwroot/www.bgoom.top/vendor/psy/psysh/src/Exception/ParseErrorException.php:44)
[stacktrace]
#0 /www/wwwroot/www.bgoom.top/vendor/psy/psysh/src/CodeCleaner.php(306): Psy\\Exception\\ParseErrorException::fromParseError()
#1 /www/wwwroot/www.bgoom.top/vendor/psy/psysh/src/CodeCleaner.php(240): Psy\\CodeCleaner->parse()
#2 /www/wwwroot/www.bgoom.top/vendor/psy/psysh/src/Shell.php(852): Psy\\CodeCleaner->clean()
#3 /www/wwwroot/www.bgoom.top/vendor/psy/psysh/src/Shell.php(881): Psy\\Shell->addCode()
#4 /www/wwwroot/www.bgoom.top/vendor/psy/psysh/src/Shell.php(1390): Psy\\Shell->setCode()
#5 /www/wwwroot/www.bgoom.top/vendor/laravel/tinker/src/Console/TinkerCommand.php(76): Psy\\Shell->execute()
#6 /www/wwwroot/www.bgoom.top/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Laravel\\Tinker\\Console\\TinkerCommand->handle()
#7 /www/wwwroot/www.bgoom.top/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#8 /www/wwwroot/www.bgoom.top/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#9 /www/wwwroot/www.bgoom.top/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#10 /www/wwwroot/www.bgoom.top/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()
#11 /www/wwwroot/www.bgoom.top/vendor/laravel/framework/src/Illuminate/Console/Command.php(211): Illuminate\\Container\\Container->call()
#12 /www/wwwroot/www.bgoom.top/vendor/symfony/console/Command/Command.php(326): Illuminate\\Console\\Command->execute()
#13 /www/wwwroot/www.bgoom.top/vendor/laravel/framework/src/Illuminate/Console/Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#14 /www/wwwroot/www.bgoom.top/vendor/symfony/console/Application.php(1096): Illuminate\\Console\\Command->run()
#15 /www/wwwroot/www.bgoom.top/vendor/symfony/console/Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand()
#16 /www/wwwroot/www.bgoom.top/vendor/symfony/console/Application.php(175): Symfony\\Component\\Console\\Application->doRun()
#17 /www/wwwroot/www.bgoom.top/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(201): Symfony\\Component\\Console\\Application->run()
#18 /www/wwwroot/www.bgoom.top/artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#19 {main}
"} 
