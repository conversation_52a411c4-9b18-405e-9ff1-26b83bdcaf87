APP_NAME={title}
APP_ENV=local
APP_KEY={app_key}
APP_DEBUG=true
APP_URL={app_url}

LOG_CHANNEL=stack

# 数据库配置
DB_CONNECTION=mysql
DB_HOST={db_host}
DB_PORT={db_port}
DB_DATABASE={db_database}
DB_USERNAME={db_username}
DB_PASSWORD={db_password}

# redis配置
REDIS_HOST={redis_host}
REDIS_PASSWORD={redis_password}
REDIS_PORT={redis_port}

BROADCAST_DRIVER=log
SESSION_DRIVER=file
SESSION_LIFETIME=120


# 缓存配置
# file为磁盘文件  redis为内存级别
# redis为内存需要安装好redis服务端并配置
CACHE_DRIVER=redis

# 异步消息队列
# sync为同步  redis为异步
# 使用redis异步需要安装好redis服务端并配置
QUEUE_CONNECTION=redis

# 后台语言
## zh_CN 简体中文
## zh_TW 繁体中文
## en    英文
DUJIAO_ADMIN_LANGUAGE=zh_CN

# 后台登录地址
ADMIN_ROUTE_PREFIX={admin_path}

# 是否开启https (前端开启了后端也必须为true)
# 后台登录出现0err或者其他登录异常问题，大概率是开启了https而后台没有开启，把下面的false改为true即可
ADMIN_HTTPS=false
