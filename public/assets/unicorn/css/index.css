.recommend p {
    font-size: 14px;
}

.category {
    padding: 40px 0;
}

.separator {
    display: block;
    width: 60px;
    height: 5px;
    margin: 15px auto 15px;
    background-color: #cccccc;
    position: relative;
    border: 1px solid #cccccc;
}

.separator:after {
    width: 30px;
    height: 5px;
    background: #333333;
    position: absolute;
    top: -1px;
    left: -1px;
    content: "";
}

.category-menus li {
    display: inline-block;
    margin: 4px;
}

.goods-buy {
    line-height: 40px;
    font-size: 13px;
}


.goods-introduction-cate {
    display: -webkit-box;
    overflow: hidden;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}



.buy-form label {
    font-size: 16px;
}