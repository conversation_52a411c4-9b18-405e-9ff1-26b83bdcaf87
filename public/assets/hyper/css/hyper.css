/* app-creative.min.css */
.modal-header {
    padding: 6px 12px !important;
}
.btn-danger:focus,
.btn-danger:not(:disabled):not(.disabled):active:focus,
.btn-outline-primary:focus,
.btn-primary:focus,
.btn-outline-primary:not(:disabled):not(.disabled):active:focus,
.btn-primary:not(:disabled):not(.disabled):active:focus {
    box-shadow: none;
}
body, .wrapper, .content-page {
    overflow: visible;
}

/* header */
.header-navbar {
    height: 70px;
   background-color: #432a2a;
    box-shadow: 0 0 35px 0 rgb(154 161 171 / 15%);
}
.header-flex {
    height: 70px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.logo-title {
    font-size: 24px;
    font-weight: 700;
    display: inline-block;
    color: #000;
    margin-left: 10px;
    vertical-align: middle;
    font-family: "SimHei";
    display: none;
}

body {
    background-color: #f0f2f5;
    height: 100%;
    overflow-x: hidden; 
}
body[data-leftbar-compact-mode=condensed] {
    min-height: 0;
}
body[data-layout=topnav] .content-page {
    padding: 0!important;
    min-height: 0;
}
.content {
    margin-bottom: 69px;
}
.page-title-right {
     /* display: block !important; */
    float: right !important;
    margin-top: 17px !important;
}
@media screen and (max-width: 380px) {
    .app-search {
        width: 160px;
    }
}
.hyper-wrapper a {
    color: #000;
}
.hyper-footer {
    position: absolute;
    width: 100%;
   
    padding: 20px 0;
    line-height: 16px;
    border-top: 1px solid rgba(152,166,173,.2);
    color: #98a6ad;
}

.hyper-footer a {
    color: #919ca7;
}
@media screen and (max-width: 576px) {
    .container {
       padding: 0 14px; 
    }
}
@media screen and (min-width: 576px) {
    .container {
       padding: 0; 
    }
}
@media screen and (min-width: 769px) {
    .hyper-sm-last {
        -webkit-box-ordinal-group: 14;
        -ms-flex-order: 13;
        order: 13;
    }
}

/* Home Page */

/* modal-dialog */
.modal-body img {
    max-width: 100%;
    height: auto;
}
@media screen and (min-width: 1330px) {
    .modal-dialog {
        max-width: 900px !important;
    }
}
@media screen and (max-width: 1330px) {
    .modal-dialog {
        max-width: 700px !important;
    }
}


/* Select Button */

.nav-list {
    margin-top: 15px;
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    margin-bottom: 12px;
}
.nav-list::-webkit-scrollbar {
    display: none; 
}
@media screen and (min-width: 769px) {
    .tab-link {
        min-width: 90px;
    }
}
@media screen and (max-width: 769px) {
    .tab-link {
        min-width: 60px;
    }
}
.tab-link {
        font-size: 15px;
    padding: 6px 10px 4px;
    border-radius: .4rem;
    text-align: center;
    position: relative;
    overflow: hidden;
    color: #676767;
    border: 1px solid #8d8d8d;
    background-image: linear-gradient( #ffffff, #f5f7fa );
}
.tab-link.active {
    color: #fff;
    background: linear-gradient(-45deg, #3369ff, #3798f7);
    border: 1px solid #3264df
}
.img-checkmark img {
    width: 36px;
}
.tab-link.active .img-checkmark {
    display: block;
}
.img-checkmark {
    position: absolute;
    opacity: 0.8;
    right: -6px;
    bottom: -12px;
    display: none;
}

.home-card {
    box-shadow: 0 3px 6px 0 rgb(0 0 0 / 12%);
    transition: all .5s;
}
.home-card:hover {
    box-shadow: 0px 0px 5px 0 #F44336;
}
@media screen and (max-width: 780px) {
    .hyper-wrapper {
        padding-left: 0;
        padding-right: 0;
        display: inline;
        /*grid-gap: 12px;
        gap: 20px;*/
    }
    .home-card {
        padding: 12px;
        margin-bottom: 18px;
        font-size: 14px;
        display: flex;
        position: relative;
        align-items: flex-start;
    	border-radius: 5px;
    	cursor: pointer;
        background-color: #fff;
    box-shadow: 3px 3px 5px 0px rgba(0,0,0,0.15);
    border: 2px solid #fff;
    background-image: linear-gradient( #f3f5f9, #ffffff );;
    	
    }
    .home-img {
        max-width: 80px;
        margin-right: 12px;
        border-radius: 0.85rem;
        border: 1px solid #c9c9c9;
        box-shadow: 2px 2px 3px rgba(0, 0, 0, 0.2);
    }
    
    span.mshu1 {
    font-size: 12px;
    color: #838383;
    position: relative;
    top: -20px;
}

    .biaoti {
    overflow: hidden;
    text-overflow: ellipsis;
}
    .flex {
        display: flex;
        flex-direction: column;
    }
    .name {
        font-size: 14px;
    	display: -webkit-box;
        -webkit-line-clamp: 2;
    	-webkit-box-orient: vertical;
    	overflow: hidden;
    	text-align: left;
    	font-family: usaxhj,pingfang SC, helvetica neue, arial, hiragino sans gb, microsoft yahei ui, microsoft yahei, simsun, sans-serif;
    color: #333;
    }
    .price {
        color: #ff468b;
        vertical-align: text-top;

    }
    .price b {
        font-size: 16px;
    }
    
    
   .zdong {
    border: 1px solid #18bc9c;
    font-size: 12px;
    text-align: center;
    padding: 1px 8px;
    border-radius: 3px;
    color: #18bc9c;
    margin-top: 20px;
}

.sdong {
    border: 1px solid #ff8600;
    font-size: 12px;
    text-align: center;
    padding: 1px 8px;
    border-radius: 3px;
    color: #ff8600;
    margin-top: 20px;
}


.kucun {
    border: 1px solid #b636d5;
    font-size: 12px;
    text-align: center;
    padding: 1px 8px;
    margin: 0px 4px 0px 0px;
    border-radius: 3px;
    color: #b636d5;
    margin-right: 3px;
}

  
.dibu {
  position: absolute;
  left: 105px;
  bottom: 12px;
}
    
    
}




@media screen and (min-width: 781px) {
    .hyper-wrapper {
        padding-left: 0;
        padding-right: 0;
        display: grid;
        grid-template-columns: repeat(2,minmax(0,1fr));
        grid-gap: 20px;
    }
    .home-card {
        padding: 12px;
        font-size: 14px;
        display: flex;
        flex-direction: column;
    	border-radius: 8px;
    	cursor: pointer;
    	position: relative;
        background-color: #fff;
        box-shadow:3px 3px 5px 0px rgba(0,0,0,0.15);
        border: 2px solid #fff;
        background-image: linear-gradient( #f3f5f9, #ffffff );

    }
    .home-img {
        width: 100px;
        border-radius: 18px;
        height: 100px;
        border: 1px solid #c9c9c9;
        box-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);

    }
    
    .biaoti {
    margin-left: -5px;
    }
    
    span.mshu1 {
    margin-left: 120px;
    font-size: 12px;
    color: #838383;
    /* position: relative; */
    /* top: -8px; */
    }
    
    .zdong {
    border: 1px solid #18bc9c;
    font-size: 12px;
    text-align: center;
    display: inline-block;
    padding: 2px 6px;
    border-radius: 3px;
    color: #18bc9c;

    }

    .sdong {
    border: 1px solid #ff8600;
    font-size: 12px;
    text-align: center;
    display: inline-block;
    padding: 2px 6px;
    border-radius: 3px;
    color: #ff8600;
    }
    
  .kucun {
    border: 1px solid #b636d5;
    font-size: 12px;
    text-align: center;
    padding: 2px 6px;
    border-radius: 3px;
    color: #b636d5;
    margin: 0 5x 0 3px;

    }

   
 .dibu {
  position: absolute;
  left: 132px;
  bottom: 11px;
    }   
    
    .flex {
        display: flex;
        flex-direction: column;
        margin-top: 12px;
    }
.name {
       font-size: 15px;
       min-height: 42px;
       margin: -100px 0px 0px 125px;
       display: -webkit-box;
       -webkit-line-clamp: 2;
       -webkit-box-orient: vertical;
       overflow: hidden;
       font-family: usaxhj,pingfang SC, helvetica neue, arial, hiragino sans gb, microsoft yahei ui, microsoft yahei, simsun, sans-serif;
    color: #333;
    }
    .price {
        color: #ff468b;
        vertical-align: middle;

    }
    .price b {
        font-size: 18px;
    }
}

@media screen and (min-width: 1200px) {
    .hyper-wrapper {
        padding-left: 0;
        padding-right: 0;
        display: grid;
        grid-template-columns: repeat(3,minmax(0,1fr));
        grid-gap: 20px;
    }
    .home-card {
        padding: 12px;
        font-size: 14px;
        display: flex;
        flex-direction: column;
    	border-radius: 8px;
    	cursor: pointer;
    	position: relative;
        background-color: #fff;
        box-shadow:3px 3px 5px 0px rgba(0,0,0,0.15);
        border: 2px solid #fff;
        background-image: linear-gradient( #f3f5f9, #ffffff );

    }
    .home-img {
        width: 100px;
        border-radius: 18px;
        height: 100px;
        border: 1px solid #c9c9c9;
        box-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);

    }
    
    .biaoti {
    margin-left: -5px;
    }
    
    span.mshu1 {
    margin-left: 120px;
    font-size: 12px;
    color: #838383;
    /* position: relative; */
    /* top: -8px; */
    }
    
    .zdong {
    border: 1px solid #18bc9c;
    font-size: 12px;
    text-align: center;
    display: inline-block;
    padding: 2px 6px;
    border-radius: 3px;
    color: #18bc9c;

    }

    .sdong {
    border: 1px solid #ff8600;
    font-size: 12px;
    text-align: center;
    display: inline-block;
    padding: 2px 6px;
    border-radius: 3px;
    color: #ff8600;
    }
    
  .kucun {
    border: 1px solid #b636d5;
    font-size: 12px;
    text-align: center;
    padding: 2px 6px;
    border-radius: 3px;
    color: #b636d5;
    margin: 0 5x 0 3px;

    }

   
 .dibu {
  position: absolute;
  left: 132px;
  bottom: 11px;
    }   
    
    .flex {
        display: flex;
        flex-direction: column;
        margin-top: 12px;
    }
.name {
       font-size: 15px;
       min-height: 42px;
       margin: -100px 0px 0px 125px;
       display: -webkit-box;
       -webkit-line-clamp: 2;
       -webkit-box-orient: vertical;
       overflow: hidden;
       font-family: usaxhj,pingfang SC, helvetica neue, arial, hiragino sans gb, microsoft yahei ui, microsoft yahei, simsun, sans-serif;
    color: #333;
    }
    .price {
        color: #ff468b;
        vertical-align: middle;

    }
    .price b {
        font-size: 18px;
    }
}

/* Buy Page*/
.form-group h3 {
    font-weight: 400;

}
.geetest_holder.geetest_wind {
    width: 100% !important;
    min-width: 100% !important;
}
.buy-product img {
    max-width:100%;
    height: auto;
    border-radius: 5px;
    cursor: pointer;
}
@media screen and (min-width: 769px) {
    .buy-grid {
        display: grid;
        grid-template-columns: repeat(6, minmax(0, 1fr));
        gap: 12px;
    }
    .sticky {
        position: -webkit-sticky;
        position: sticky;
        top: 6px;
    }
    .buy-shop {
        grid-column: span 2 / span 2;
    }
    .buy-product {
        grid-column: span 4 / span 4;
    }
}
/* pay-type */
@media screen and (min-width: 1330px) {
    .pay-grid {
        display: grid;
        grid-template-columns: repeat(2,minmax(0,200px));
        grid-gap: 12px;
    }
}
@media screen and (min-width: 991px) and (max-width: 1330px) {
    .pay-grid {
        display: grid;
        grid-template-columns: repeat(1,minmax(0,300px));
        grid-gap: 6px;
    }
}

@media screen and (max-width: 576px) {
    .pay-grid {
        display: grid;
        grid-template-columns: repeat(2,minmax(0,200px));
        grid-gap: 12px;
    }
}
@media screen and (max-width: 380px) {
    .pay-grid {
        display: grid;
        grid-template-columns: repeat(1,minmax(0,300px));
        grid-gap: 6px;
    }
}
.pay-type {
    background-color: #fff;
    color: #000;
    border: 2px solid #bdcfe1;
}
.pay-type:hover {
    background-color: #fff;
    color: #000;
}
.pay-type.active {
    background-color: #fff;
    color: #3688fc;
    border: 2px solid #3688fc;
}
.buy-price {
    color: #ea5455;
}

/* Orderinfo Page */

.orderinfo-grid {
    display: grid;
    grid-template-columns: auto;
}
@media screen and (min-width: 769px) {
    .orderinfo-card-grid {
        display: grid;
        grid-template-columns: repeat(6,minmax(0,1fr));
        grid-gap: 12px;
    }
    .orderinfo-info {
        grid-column: span 2 / span 2;
    }
    .orderinfo-kami {
        grid-column: span 4 / span 4;
    }
}
@media screen and (max-width: 769px) {
    .orderinfo-info {
        display: grid;
        justify-content: center;
    }
}
.textarea-kami {
    min-height: calc(100% - 48px - 38px);
    color: #e3812a;
}
.kami-btn {
    margin-top: 6px;
    float: right;
}

/* Footer */
.footerlink {
  color: #000;
  position: absolute;
  width: 100%;
  text-align: center;
}

.ftr {
  display:inline-block;
  width:60px;
  height: 60px;
  background: #f1f1f1;
  margin: 15px;
  border-radius: 30%;
  box-shadow:-5px 5px 15px -5px #f1f1f1; 
  overflow: hidden;
  position: relative;
  transition: 0.3s linear;
}

.ftr i {
  line-height: 60px;
  font-size: 26px;
  transition: 0.3s linear;
}

.ftr:nth-child(1) i {
  color:#3b5998;
}

.ftr:nth-child(2) i {
  color:#1da1f2;
}

.ftr:nth-child(3) i {
  color:#c32aa3;
}

.ftr:nth-child(4) i {
  color:#db4437;
}

.ftr:hover {
  transform: scale(1.1);
}

.ftr:hover i {
  transform: scale(1.2);
  color: #fff;
}

.ftr:before {
  content:"";
  position:absolute;
  width:120%;
  height:120%;
  transform: rotate(45deg);
  left: -110%;
  top:90%;
}

.ftr:nth-child(1)::before {
  background: #3b5998;
}

.ftr:nth-child(2)::before {
  background: #1da1f2;
}

.ftr:nth-child(3)::before {
  background: #c32aa3;
}

.ftr:nth-child(4)::before {
  background: #db4437;
}

.ftr:hover::before {
  animation: aaa 0.7s 1;
  top: -10%;
  left: -10%;
}



.pay-type {
    display: inline-block;
    text-align: center;
    background: #f7f7f7;
    border: 2px solid #e7e7e7;
    border-radius: 5px;
    position: relative;
    padding: 6px 7px;
    margin-top: 5px;
    margin-right: 5px;
    margin-bottom: 10px;
    cursor: pointer;
}
.pay-select {
    border: 2px solid #3369ff;
    background: #f8faff;
    color: #3369ff;
}
.tab-link {
    background-color: #fafbfe;
}
.stats-card {
    position: relative;
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 28px 26px 28px 30px;
    margin-bottom: 24px;
}
.stats-icon {
    display: block;
    width: 56px;
    height: 56px;
    display: flex;
    justify-content: center;
    align-items: center;
    background: rgba(77,85,232,.09);
    /*background: var(--primary-light);*/
    color: #4d55e8;
    color: var(--primary);
    border-radius: 50%;
}
.stats-card i {
    font-size: 24px;
}
.stats-detail {
    margin-right: auto;
    margin-left: 24px;
    display: flex;
    flex-direction: column;
    align-items: left;
    justify-content: center;
}
.stats-icon-user {
    background: rgba(7,187,7,.13);
    /*background: var(--success--light);*/
    color: #07bb07;
    color: var(--success);
}
.edu-badge, .malus-invite-tips {
    cursor: pointer;
}
.malus-invite-tips {
    display: block;
    position: absolute;
    right: 0;
    bottom: 0;
    background: #f8f9fa;
    background: var(--light);
    color: #868e96;
    color: var(--secondary);
    font-size: 12px;
    font-weight: 500;
    line-height: 1.5;
    padding: 3px 8px 3px 16px;
    text-align: center;
    border-radius: 20px 0 0 0;
}
.stats-member h6 {
    margin-top: 3px!important;
    font-size: 16px;
}
.stats-icon-time {
    background: rgba(253,151,68,.19);
    /*background: var(--orange-light);*/
    color: #fd9644;
    color: var(--orange);
}
.vip-center .card-header .card-title {
    font-size: 16px;
    font-weight: 700;
}
.btn-pill {
    border-radius: 10rem;
    padding-left: 1.5em;
    padding-right: 1.5em;
}
@media (min-width: 576px){
    .modal-sm {
        max-width: 600px !important;
    }
}
@media (max-width: 576px){
    .search-order {
        display: none;
    }
}
.order-table .table td {
    padding: 1.2rem 0 0 0 !important;
    vertical-align: center !important;
    border-top: 1px solid #f3f3f3;
}
.btn-outline-orange {
    background: rgba(7,187,7,.13);
    border: 1px solid var(--success);
    color: #07bb07;
    color: var(--success);
    background: none;
}
.overview {
    -webkit-box-align: center!important;
    -ms-flex-align: center!important;
    align-items: center!important;
}
.overview .card-title {
    font-size: 16px;
    font-weight: 600;
}
.affiliate-overview {
    align-items: center;
    display: flex;
    padding: 24px 24px 24px 48px;
}
.affiliate-overview .card {
    margin-right: 8px;
    height: 88px;
    display: flex;
    justify-content: center;
    margin-bottom: 0;
    width: 260px;
    box-shadow: none;
}

.affiliate-show-withdrowal-btn {
    margin: 0 12px 0 auto;
    height: 42px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 56px;
    position: relative;
}
.bg-success {
    background-color: rgba(7,187,7,.13)!important;
    background-color: var(--success--light)!important;
}
.stamp {
    color: #fff;
    background: #868e96;
    display: flex;
    justify-content: center;
    align-items: center;
    min-width: 48px;
    height: 48px;
    border-radius: 6px;
    margin-right: 24px!important;
}
@media (max-width: 768px){
    .affiliate-overview {
        align-items: flex-start;
        padding: 24px;
    }
    .affiliate-overview .card {
        margin-right: 0;
    }
    .affiliate-link, .affiliate-overview {
        flex-direction: column;
    }
    .affiliate-show-withdrowal-btn {
        margin-top: 24px;
        width: 100%;
    }
}
.stamp-md {
    line-height: 2.5rem;
}
.affiliate-overview .h4 {
    font-size: 26px;
}
.bg-success {
    background-color: rgba(7,187,7,.13)!important;
    /*background-color: var(--success--light)!important;*/
}
.bg-success i {
    color: #07bb07;
    color: var(--success);
}
.stamp i {
    font-size: 24px;
}
.contact {
    min-height: 136px;
}
.bg-red {
    background: rgba(235,87,87,.17)!important;
}
.bg-red i {
    color: #eb5757;
}
.invite-card .card-title {
    font-size: 16px;
    font-weight: 700;
}
.malus-share .list {
    padding-left: 18px;
    font-size: 14px;
}
.invite-table .table th {
    vertical-align: middle;
    line-height: 1;
}
.invite-table .table th {
    vertical-align: center !important;
    border-top: 1px solid #f3f3f3;
}
.withdrowal-setting .account-info {
    margin-bottom: 24px;
}
.withdrowal-setting .account-info .payment-type {
    margin-top: 24px;
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-bottom: 20px;
}
.withdrowal-setting .account-info .payment-type div.active {
    border: 1px solid #4d55e8;
}
.withdrowal-setting .account-info .payment-type div {
    cursor: pointer;
    width: 162px;
    height: 42px;
    background: #fff;
    border: 1px solid #e8e7f8;
    box-sizing: border-box;
    border-radius: 4px;
    margin-right: 16px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 14px;
    font-weight: 500;
}
.withdrowal-setting .account-info .payment-type div svg {
    margin-right: 8px;
}
.withdrowal-setting .account-info .account {
    display: flex;
    flex-direction: row;
    align-items: center;
}
.withdrowal-setting .account-info .account .form-wrapper.account-name {
    width: 340px;
    margin-right: 16px;
}
.withdrowal-setting .account-info .account .form-wrapper .label {
    font-size: 13px;
    font-weight: 500;
    margin-bottom: 8px;
}
.withdrowal-setting .account-info .account .form-wrapper.account-name input {
    width: 100%;
}
.withdrowal-setting .account-info .account .form-wrapper input {
    background: #fff;
    border: 1px solid #dcdcdc;
    box-sizing: border-box;
    border-radius: 4px;
    font-size: 12px;
    line-height: 17px;
    display: flex;
    align-items: center;
    color: #7b7b7b;
    margin-right: 16px;
    padding: 0 12px;
    height: 46px;
    margin-bottom: 16px;
}
.withdrowal-setting .desc {
    margin-top: 18px;
    position: relative;
}
.withdrowal-setting .desc h4 {
    margin-bottom: 24px;
}
.withdrowal-setting .desc li {
    list-style: none;
    font-size: 12px;
    line-height: 17px;
    display: flex;
    align-items: center;
    color: #666;
    flex: none;
    order: 0;
    align-self: flex-start;
    margin-top: 12px;
}
.withdrowal-setting .desc button {
    margin-top: 42px;
    border-radius: 4px;
    width: 212px;
    height: 47px;
    float: right;
}
.invite-table .show-address {
    max-width: 180px;
}
.choose-tag {
    display: flex;
    flex-wrap: wrap;
    margin: 15px 0px;
}
.choose-tag .tag.active {
    border-color: #4d55e8;
    color: #4d55e8;
}
.choose-tag .tag {
    font-size: 15px;
    border-radius: 3px;
    margin-right: 8px;
    border: 1px solid #e0e0e0;
    font-weight: 400;
    background: none;
    color: #333;
    position: relative;
    padding: 4px 8px;
    min-width: 100px;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 8px;
}
.choose-tag .tag .discount-tag {
    color: #fff;
    background: #ff4243;
    padding: 2px 6px;
    border-radius: 100px;
    font-size: 12px;
    font-weight: 700;
    line-height: 14px;
    margin-left: 4px;
    transform: scale(.9);
    margin-top: -1px;
}
.body-dark {
    background: #18191a !important;
}
.header-dark {
    background-color: #383838 !important;
}
.card-dark {
    background-color: #383838 !important;
}
.tab-link-dark {
    background-color: #383838 !important;
}
.home-card-dark {
    background-color: #383838 !important;
}
.form-control-dark {
    background-color: #383838 !important;
}
.card-header-dark {
    background-color: #383838 !important;
}
.modal-content-dark {
    background-color: #383838 !important;
}
.name-dark {
    color: #d2d2d2 !important;
}
