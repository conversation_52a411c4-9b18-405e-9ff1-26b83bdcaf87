{"name": "dujiaoka/geetest", "description": "Enterprise-grade Geetest CAPTCHA package for Laravel 10+ and PHP 8.2+", "type": "library", "license": "MIT", "keywords": ["laravel", "geetest", "<PERSON><PERSON>a", "validation", "security"], "authors": [{"name": "Dujiaoka Team", "email": "<EMAIL>"}], "require": {"php": "^8.1", "laravel/framework": "^10.0", "ext-curl": "*", "ext-json": "*"}, "require-dev": {"phpunit/phpunit": "^10.0", "orchestra/testbench": "^8.0"}, "autoload": {"psr-4": {"Dujiaoka\\Geetest\\": "src/"}}, "autoload-dev": {"psr-4": {"Dujiaoka\\Geetest\\Tests\\": "tests/"}}, "extra": {"laravel": {"providers": ["Dujiaoka\\Geetest\\GeetestServiceProvider"], "aliases": {"Geetest": "Dujiaoka\\Geetest\\Facades\\Geetest"}}}, "minimum-stability": "stable", "prefer-stable": true}